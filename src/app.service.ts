import { Injectable } from '@nestjs/common';

// Creating a unique process ID based on the current date, because the process ID will be always "1" in a Docker container.
// This is a workaround to provide a unique identifier for the process in a containerized environment for demo purposes.
const PROCESS_ID = new Date().getTime();

@Injectable()
export class AppService {
  getHello(): { message: string; processId: number; timestamp: string } {
    return {
      message: 'Hello World from NestJS!',
      processId: PROCESS_ID,
      timestamp: new Date().toISOString(),
    };
  }

  getProcessId(): { processId: number; timestamp: string } {
    return {
      processId: PROCESS_ID,
      timestamp: new Date().toISOString(),
    };
  }

  getHealth(): { status: string; processId: number; timestamp: string } {
    return {
      status: 'OK',
      processId: PROCESS_ID,
      timestamp: new Date().toISOString(),
    };
  }
}
